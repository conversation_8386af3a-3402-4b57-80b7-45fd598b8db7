#!/bin/bash
set -e

# Create a Docker container to build the Lambda layer with the correct architecture
echo "Creating Docker container for Lambda layer build..."

# Create a temporary Dockerfile
cat > Dockerfile.layer << EOF
FROM public.ecr.aws/lambda/python:3.13

# Copy requirements file
COPY layers/powertools/requirements.txt /tmp/requirements.txt

# Install dependencies
RUN pip install --no-cache-dir -r /tmp/requirements.txt -t /opt/python/

# Create the layer structure
RUN mkdir -p /opt/python && \
    cd /opt && \
    zip -r /tmp/lambda-layer.zip python/

# Output directory
WORKDIR /tmp
EOF

# Build the Docker image
echo "Building Docker image..."
docker build -t lambda-layer-builder -f Dockerfile.layer .

# Run the container to extract the layer
echo "Extracting Lambda layer..."
docker run --rm -v $(pwd):/output lambda-layer-builder cp /tmp/lambda-layer.zip /output/lambda-layer.zip

# Clean up
rm Dockerfile.layer

echo "Lambda layer built successfully: lambda-layer.zip"
echo "Now you can publish it with: aws lambda publish-layer-version --layer-name fundflow-dependencies --zip-file fileb://lambda-layer.zip --compatible-runtimes python3.13"
