AWSTemplateFormatVersion: "2010-09-09"
Transform: AWS::Serverless-2016-10-31
Description: >
  FundFlow - Serverless application for fund management platform

Globals:
  Function:
    Timeout: 30
    MemorySize: 512
    Runtime: python3.11
    Environment:
      Variables:
        POWERTOOLS_SERVICE_NAME: fundflow
        POWERTOOLS_METRICS_NAMESPACE: fundflow
        LOG_LEVEL: INFO
        ENVIRONMENT: !Ref Environment
        COGNITO_USER_POOL_ID: !Ref ExistingUserPoolId
        COGNITO_APP_CLIENT_ID: !Ref ExistingUserPoolClientId

Parameters:
  Environment:
    Type: String
    Default: dev
    AllowedValues: [dev, staging, prod]
    Description: Environment name

  DomainName:
    Type: String
    Default: ""
    Description: Custom domain name for the API (optional)

  CertificateArn:
    Type: String
    Default: ""
    Description: ACM certificate ARN for custom domain (required if DomainName is provided)

  LogLevel:
    Type: String
    Default: INFO
    AllowedValues: [DEBUG, INFO, WARNING, ERROR]
    Description: Lambda function log level

  LambdaMemorySize:
    Type: Number
    Default: 512
    MinValue: 128
    MaxValue: 10240
    Description: Default memory size for Lambda functions (MB)

  LambdaTimeout:
    Type: Number
    Default: 30
    MinValue: 3
    MaxValue: 900
    Description: Default timeout for Lambda functions (seconds)

  ApiThrottleBurstLimit:
    Type: Number
    Default: 200
    MinValue: 0
    MaxValue: 5000
    Description: API Gateway throttle burst limit

  ApiThrottleRateLimit:
    Type: Number
    Default: 100
    MinValue: 0
    MaxValue: 10000
    Description: API Gateway throttle rate limit (requests per second)

  # DynamoDB Configuration
  DynamoDBBillingMode:
    Type: String
    Default: PAY_PER_REQUEST
    AllowedValues: [PAY_PER_REQUEST, PROVISIONED]
    Description: DynamoDB billing mode

  DynamoDBReadCapacity:
    Type: Number
    Default: 5
    MinValue: 1
    MaxValue: 40000
    Description: DynamoDB read capacity units (for PROVISIONED billing)

  DynamoDBWriteCapacity:
    Type: Number
    Default: 5
    MinValue: 1
    MaxValue: 40000
    Description: DynamoDB write capacity units (for PROVISIONED billing)

  BackupRetentionDays:
    Type: Number
    Default: 30
    MinValue: 1
    MaxValue: 2555
    Description: DynamoDB backup retention period in days

  # CloudWatch Configuration
  LogRetentionDays:
    Type: Number
    Default: 30
    AllowedValues:
      [
        1,
        3,
        5,
        7,
        14,
        30,
        60,
        90,
        120,
        150,
        180,
        365,
        400,
        545,
        731,
        1827,
        3653,
      ]
    Description: CloudWatch log retention period in days

  EnableDetailedMonitoring:
    Type: String
    Default: "false"
    AllowedValues: ["true", "false"]
    Description: Enable detailed CloudWatch monitoring

  AlarmNotificationEmail:
    Type: String
    Default: ""
    Description: Email address for CloudWatch alarm notifications (optional)

  # API Gateway Advanced Configuration
  ApiCachingEnabled:
    Type: String
    Default: "false"
    AllowedValues: ["true", "false"]
    Description: Enable API Gateway caching

  ApiCacheTTL:
    Type: Number
    Default: 300
    MinValue: 0
    MaxValue: 3600
    Description: API Gateway cache TTL in seconds

  RequestValidationMode:
    Type: String
    Default: basic
    AllowedValues: [none, basic, full]
    Description: API Gateway request validation mode

  # CloudFront Configuration
  CloudFrontPriceClass:
    Type: String
    Default: PriceClass_100
    AllowedValues: [PriceClass_100, PriceClass_200, PriceClass_All]
    Description: CloudFront price class for geographic distribution

  CloudFrontCompressionEnabled:
    Type: String
    Default: "true"
    AllowedValues: ["true", "false"]
    Description: Enable CloudFront compression

  CloudFrontDefaultTTL:
    Type: Number
    Default: 86400
    MinValue: 0
    MaxValue: 31536000
    Description: CloudFront default cache TTL in seconds

  CloudFrontMaxTTL:
    Type: Number
    Default: 31536000
    MinValue: 0
    MaxValue: 31536000
    Description: CloudFront maximum cache TTL in seconds

  # Security Configuration
  EnableWAF:
    Type: String
    Default: "false"
    AllowedValues: ["true", "false"]
    Description: Enable AWS WAF for API Gateway

  SecurityHeadersEnabled:
    Type: String
    Default: "true"
    AllowedValues: ["true", "false"]
    Description: Enable security headers in responses

  IPWhitelistEnabled:
    Type: String
    Default: "false"
    AllowedValues: ["true", "false"]
    Description: Enable IP whitelist restrictions

  # Cognito Configuration (existing resources)
  ExistingUserPoolId:
    Type: String
    Default: ""
    Description: Existing Cognito User Pool ID (leave empty to create new)

  ExistingUserPoolClientId:
    Type: String
    Default: ""
    Description: Existing Cognito User Pool Client ID (leave empty to create new)

  # New Cognito Configuration Parameters
  CreateCognitoResources:
    Type: String
    Default: "true"
    AllowedValues: ["true", "false"]
    Description: Whether to create Cognito resources or use existing ones

  # AI Service Configuration
  OpenRouterAPIKey:
    Type: String
    NoEcho: true
    Description: OpenRouter API key for AI-powered PDF fund extraction (required for PDF extraction feature)

  CognitoUserPoolName:
    Type: String
    Default: "fundflow-user-pool"
    Description: Name for the Cognito User Pool

  CognitoClientName:
    Type: String
    Default: "fundflow-client"
    Description: Name for the Cognito User Pool Client

  CognitoResourceServerName:
    Type: String
    Default: "fundflow-api"
    Description: Name for the Cognito Resource Server

  # OAuth Configuration
  CallbackURLs:
    Type: CommaDelimitedList
    Default: "http://localhost:3000/auth/callback,https://your-frontend-domain.com/auth/callback"
    Description: Allowed callback URLs for OAuth flows

  LogoutURLs:
    Type: CommaDelimitedList
    Default: "http://localhost:3000/auth/signout,https://your-frontend-domain.com/auth/signout"
    Description: Allowed logout URLs for OAuth flows

Conditions:
  IsProduction: !Equals [!Ref Environment, prod]
  HasCustomDomain: !Not [!Equals [!Ref DomainName, ""]]
  IsProvisionedBilling: !Equals [!Ref DynamoDBBillingMode, PROVISIONED]
  HasAlarmEmail: !Not [!Equals [!Ref AlarmNotificationEmail, ""]]
  CreateNewCognito: !Equals [!Ref CreateCognitoResources, "true"]

Resources:
  # Lambda Powertools Layer
  PowertoolsLayer:
    Type: AWS::Serverless::LayerVersion
    Properties:
      LayerName: !Sub "${AWS::StackName}-powertools"
      Description: Lambda Powertools for Python
      ContentUri: layers/powertools/
      CompatibleRuntimes:
        - python3.13
      RetentionPolicy: Retain
    Metadata:
      BuildMethod: python3.13

  # Cognito Resources
  UserPool:
    Type: AWS::Cognito::UserPool
    Condition: CreateNewCognito
    Properties:
      UserPoolName: !Sub "${AWS::StackName}-${CognitoUserPoolName}"
      AliasAttributes:
        - email
        - preferred_username
      AutoVerifiedAttributes:
        - email
      Policies:
        PasswordPolicy:
          MinimumLength: 8
          RequireUppercase: true
          RequireLowercase: true
          RequireNumbers: true
          RequireSymbols: false
      Schema:
        - Name: email
          AttributeDataType: String
          Required: true
          Mutable: false
        - Name: given_name
          AttributeDataType: String
          Required: false
          Mutable: true
        - Name: family_name
          AttributeDataType: String
          Required: false
          Mutable: true
      UsernameConfiguration:
        CaseSensitive: false
      UserPoolTags:
        Environment: !Ref Environment
        Application: FundFlow

  UserPoolResourceServer:
    Type: AWS::Cognito::UserPoolResourceServer
    Condition: CreateNewCognito
    Properties:
      UserPoolId: !Ref UserPool
      Identifier: !Ref CognitoResourceServerName
      Name: !Sub "${AWS::StackName} API Resource Server"
      Scopes:
        - ScopeName: "admin:all"
          ScopeDescription: "Full administrative access"
        - ScopeName: "funds:read"
          ScopeDescription: "Read fund data"
        - ScopeName: "funds:write"
          ScopeDescription: "Create and update fund data"
        - ScopeName: "users:read"
          ScopeDescription: "Read user data"
        - ScopeName: "users:write"
          ScopeDescription: "Create and update user data"
        - ScopeName: "reports:read"
          ScopeDescription: "Read reports"
        - ScopeName: "reports:write"
          ScopeDescription: "Generate and update reports"

  UserPoolClient:
    Type: AWS::Cognito::UserPoolClient
    Condition: CreateNewCognito
    DependsOn: UserPoolResourceServer
    Properties:
      UserPoolId: !Ref UserPool
      ClientName: !Sub "${AWS::StackName}-${CognitoClientName}"
      GenerateSecret: false
      ExplicitAuthFlows:
        - ALLOW_USER_PASSWORD_AUTH
        - ALLOW_ADMIN_USER_PASSWORD_AUTH
        - ALLOW_REFRESH_TOKEN_AUTH
        - ALLOW_USER_SRP_AUTH
        - ALLOW_USER_AUTH
      SupportedIdentityProviders:
        - COGNITO
      CallbackURLs: !Ref CallbackURLs
      LogoutURLs: !Ref LogoutURLs
      AllowedOAuthFlows:
        - code
        - implicit
      AllowedOAuthScopes:
        - email
        - openid
        - profile
        - aws.cognito.signin.user.admin
        - !Sub "${CognitoResourceServerName}/admin:all"
        - !Sub "${CognitoResourceServerName}/funds:read"
        - !Sub "${CognitoResourceServerName}/funds:write"
        - !Sub "${CognitoResourceServerName}/users:read"
        - !Sub "${CognitoResourceServerName}/users:write"
        - !Sub "${CognitoResourceServerName}/reports:read"
        - !Sub "${CognitoResourceServerName}/reports:write"
      AllowedOAuthFlowsUserPoolClient: true
      RefreshTokenValidity: 30
      AccessTokenValidity: 720
      IdTokenValidity: 720
      TokenValidityUnits:
        AccessToken: minutes
        IdToken: minutes
        RefreshToken: days
      PreventUserExistenceErrors: ENABLED

  # Lambda Functions
  HealthCheckFunction:
    Type: AWS::Serverless::Function
    Properties:
      CodeUri: src/
      Handler: functions.api.health.handler
      MemorySize: !Ref LambdaMemorySize
      Timeout: !Ref LambdaTimeout
      Layers:
        - !Ref PowertoolsLayer
      Environment:
        Variables:
          ENVIRONMENT: !Ref Environment
          API_VERSION: "1.0.0"
          LOG_LEVEL: !Ref LogLevel
          POWERTOOLS_SERVICE_NAME: fundflow-health
          POWERTOOLS_METRICS_NAMESPACE: FundFlow
      Events:
        HealthAPI:
          Type: Api
          Properties:
            RestApiId: !Ref ApiGateway
            Path: /health
            Method: get
            Auth:
              Authorizer: NONE
        HealthOptions:
          Type: Api
          Properties:
            RestApiId: !Ref ApiGateway
            Path: /health
            Method: options
            Auth:
              Authorizer: NONE

  FundsAPIFunction:
    Type: AWS::Serverless::Function
    Properties:
      CodeUri: src/
      Handler: functions.api.funds.handler
      MemorySize: !Ref LambdaMemorySize
      Timeout: !Ref LambdaTimeout
      Layers:
        - !Ref PowertoolsLayer
        - arn:aws:lambda:ap-northeast-1:770693421928:layer:Klayers-p311-pydantic:18
      Environment:
        Variables:
          ENVIRONMENT: !Ref Environment
          FUND_TABLE: !Ref FundTable
          HISTORICAL_PRICES_TABLE: !Ref HistoricalPricesTable
          FUND_SNAPSHOT_TABLE: !Ref FundSnapshotTable
          USER_TABLE: !Ref UserTable
          STATIC_ASSETS_BUCKET: !Ref StaticAssetsBucket
          USER_POOL_ID:
            !If [CreateNewCognito, !Ref UserPool, !Ref ExistingUserPoolId]
          USER_POOL_CLIENT_ID:
            !If [
              CreateNewCognito,
              !Ref UserPoolClient,
              !Ref ExistingUserPoolClientId,
            ]
          LOG_LEVEL: !Ref LogLevel
          POWERTOOLS_SERVICE_NAME: fundflow-funds-api
          POWERTOOLS_METRICS_NAMESPACE: FundFlow
      Policies:
        - DynamoDBCrudPolicy:
            TableName: !Ref FundTable
        - DynamoDBCrudPolicy:
            TableName: !Ref HistoricalPricesTable
        - DynamoDBCrudPolicy:
            TableName: !Ref FundSnapshotTable
        - DynamoDBReadPolicy:
            TableName: !Ref UserTable
        - S3CrudPolicy:
            BucketName: !Ref StaticAssetsBucket
      Events:
        ListFunds:
          Type: Api
          Properties:
            RestApiId: !Ref ApiGateway
            Path: /funds
            Method: get
            Auth:
              Authorizer: CognitoAuthorizerV2
        CreateFund:
          Type: Api
          Properties:
            RestApiId: !Ref ApiGateway
            Path: /funds
            Method: post
            Auth:
              Authorizer: CognitoAuthorizerV2
        GetFund:
          Type: Api
          Properties:
            RestApiId: !Ref ApiGateway
            Path: /funds/{fundId}
            Method: get
            Auth:
              Authorizer: CognitoAuthorizerV2
        UpdateFund:
          Type: Api
          Properties:
            RestApiId: !Ref ApiGateway
            Path: /funds/{fundId}
            Method: put
            Auth:
              Authorizer: CognitoAuthorizerV2
        DeleteFund:
          Type: Api
          Properties:
            RestApiId: !Ref ApiGateway
            Path: /funds/{fundId}
            Method: delete
            Auth:
              Authorizer: CognitoAuthorizerV2
        # CORS OPTIONS endpoints for funds
        FundsOptions:
          Type: Api
          Properties:
            RestApiId: !Ref ApiGateway
            Path: /funds
            Method: options
            Auth:
              Authorizer: NONE
        FundByIdOptions:
          Type: Api
          Properties:
            RestApiId: !Ref ApiGateway
            Path: /funds/{fundId}
            Method: options
            Auth:
              Authorizer: NONE
        # Fund Market Data endpoints
        GetFundMarketData:
          Type: Api
          Properties:
            RestApiId: !Ref ApiGateway
            Path: /funds/{fundId}/market-data
            Method: get
            Auth:
              Authorizer: CognitoAuthorizerV2
        PostFundMarketData:
          Type: Api
          Properties:
            RestApiId: !Ref ApiGateway
            Path: /funds/{fundId}/market-data
            Method: post
            Auth:
              Authorizer: CognitoAuthorizerV2
        # Fund Details endpoints
        GetFundDetails:
          Type: Api
          Properties:
            RestApiId: !Ref ApiGateway
            Path: /funds/{fundId}/details
            Method: get
            Auth:
              Authorizer: CognitoAuthorizerV2
        # Fund Historical Data endpoints
        GetFundHistorical:
          Type: Api
          Properties:
            RestApiId: !Ref ApiGateway
            Path: /funds/{fundId}/historical
            Method: get
            Auth:
              Authorizer: CognitoAuthorizerV2
        # CORS OPTIONS for market-data endpoint
        FundMarketDataOptions:
          Type: Api
          Properties:
            RestApiId: !Ref ApiGateway
            Path: /funds/{fundId}/market-data
            Method: options
            Auth:
              Authorizer: NONE
        # CORS OPTIONS for details endpoint
        FundDetailsOptions:
          Type: Api
          Properties:
            RestApiId: !Ref ApiGateway
            Path: /funds/{fundId}/details
            Method: options
            Auth:
              Authorizer: NONE
        # CORS OPTIONS for historical endpoint
        FundHistoricalOptions:
          Type: Api
          Properties:
            RestApiId: !Ref ApiGateway
            Path: /funds/{fundId}/historical
            Method: options
            Auth:
              Authorizer: NONE
        # Fund Manager Photo endpoints
        UploadFundManagerPhoto:
          Type: Api
          Properties:
            RestApiId: !Ref ApiGateway
            Path: /funds/{fundId}/manager-photo
            Method: post
            Auth:
              Authorizer: CognitoAuthorizerV2
        DeleteFundManagerPhoto:
          Type: Api
          Properties:
            RestApiId: !Ref ApiGateway
            Path: /funds/{fundId}/manager-photo
            Method: delete
            Auth:
              Authorizer: CognitoAuthorizerV2
        # CORS OPTIONS for manager-photo endpoint
        FundManagerPhotoOptions:
          Type: Api
          Properties:
            RestApiId: !Ref ApiGateway
            Path: /funds/{fundId}/manager-photo
            Method: options
            Auth:
              Authorizer: NONE
        # Fund Snapshot endpoints
        ListFundSnapshots:
          Type: Api
          Properties:
            RestApiId: !Ref ApiGateway
            Path: /funds/{fundId}/snapshots
            Method: get
            Auth:
              Authorizer: CognitoAuthorizerV2
        CreateFundSnapshot:
          Type: Api
          Properties:
            RestApiId: !Ref ApiGateway
            Path: /funds/{fundId}/snapshots/{month}
            Method: post
            Auth:
              Authorizer: CognitoAuthorizerV2
        GetFundSnapshot:
          Type: Api
          Properties:
            RestApiId: !Ref ApiGateway
            Path: /funds/{fundId}/snapshots/{month}
            Method: get
            Auth:
              Authorizer: CognitoAuthorizerV2
        DeleteFundSnapshot:
          Type: Api
          Properties:
            RestApiId: !Ref ApiGateway
            Path: /funds/{fundId}/snapshots/{month}
            Method: delete
            Auth:
              Authorizer: CognitoAuthorizerV2
        # CORS OPTIONS for snapshots endpoints
        FundSnapshotsOptions:
          Type: Api
          Properties:
            RestApiId: !Ref ApiGateway
            Path: /funds/{fundId}/snapshots
            Method: options
            Auth:
              Authorizer: NONE
        FundSnapshotByMonthOptions:
          Type: Api
          Properties:
            RestApiId: !Ref ApiGateway
            Path: /funds/{fundId}/snapshots/{month}
            Method: options
            Auth:
              Authorizer: NONE

  UsersAPIFunction:
    Type: AWS::Serverless::Function
    Properties:
      CodeUri: src/
      Handler: functions.api.users.handler
      Runtime: python3.13
      MemorySize: !Ref LambdaMemorySize
      Timeout: !Ref LambdaTimeout
      Environment:
        Variables:
          ENVIRONMENT: !Ref Environment
          USER_TABLE: !Ref UserTable
          USER_POOL_ID:
            !If [CreateNewCognito, !Ref UserPool, !Ref ExistingUserPoolId]
          USER_POOL_CLIENT_ID:
            !If [
              CreateNewCognito,
              !Ref UserPoolClient,
              !Ref ExistingUserPoolClientId,
            ]
          LOG_LEVEL: !Ref LogLevel
          POWERTOOLS_SERVICE_NAME: fundflow-users-api
          POWERTOOLS_METRICS_NAMESPACE: FundFlow
      Policies:
        - DynamoDBCrudPolicy:
            TableName: !Ref UserTable
      Events:
        ListUsers:
          Type: Api
          Properties:
            RestApiId: !Ref ApiGateway
            Path: /users
            Method: get
            Auth:
              Authorizer: CognitoAuthorizerV2
        CreateUser:
          Type: Api
          Properties:
            RestApiId: !Ref ApiGateway
            Path: /users
            Method: post
            Auth:
              Authorizer: CognitoAuthorizerV2
        GetUser:
          Type: Api
          Properties:
            RestApiId: !Ref ApiGateway
            Path: /users/{userId}
            Method: get
            Auth:
              Authorizer: CognitoAuthorizerV2
        UpdateUser:
          Type: Api
          Properties:
            RestApiId: !Ref ApiGateway
            Path: /users/{userId}
            Method: put
            Auth:
              Authorizer: CognitoAuthorizerV2
        DeleteUser:
          Type: Api
          Properties:
            RestApiId: !Ref ApiGateway
            Path: /users/{userId}
            Method: delete
            Auth:
              Authorizer: CognitoAuthorizerV2
        # CORS OPTIONS endpoints for users
        UsersOptions:
          Type: Api
          Properties:
            RestApiId: !Ref ApiGateway
            Path: /users
            Method: options
            Auth:
              Authorizer: NONE
        UserByIdOptions:
          Type: Api
          Properties:
            RestApiId: !Ref ApiGateway
            Path: /users/{userId}
            Method: options
            Auth:
              Authorizer: NONE

  PortfoliosAPIFunction:
    Type: AWS::Serverless::Function
    Properties:
      CodeUri: src/
      Handler: functions.api.portfolios.handler
      Runtime: python3.13
      MemorySize: !Ref LambdaMemorySize
      Timeout: !Ref LambdaTimeout
      Environment:
        Variables:
          ENVIRONMENT: !Ref Environment
          PORTFOLIO_TABLE: !Ref PortfolioTable
          USER_TABLE: !Ref UserTable
          USER_POOL_ID:
            !If [CreateNewCognito, !Ref UserPool, !Ref ExistingUserPoolId]
          USER_POOL_CLIENT_ID:
            !If [
              CreateNewCognito,
              !Ref UserPoolClient,
              !Ref ExistingUserPoolClientId,
            ]
          LOG_LEVEL: !Ref LogLevel
          POWERTOOLS_SERVICE_NAME: fundflow-portfolios
          POWERTOOLS_METRICS_NAMESPACE: FundFlow
      Policies:
        - DynamoDBCrudPolicy:
            TableName: !Ref PortfolioTable
        - DynamoDBReadPolicy:
            TableName: !Ref UserTable
      Events:
        ListPortfolios:
          Type: Api
          Properties:
            RestApiId: !Ref ApiGateway
            Path: /portfolios
            Method: get
            Auth:
              Authorizer: CognitoAuthorizerV2
        CreatePortfolio:
          Type: Api
          Properties:
            RestApiId: !Ref ApiGateway
            Path: /portfolios
            Method: post
            Auth:
              Authorizer: CognitoAuthorizerV2
        GetPortfolio:
          Type: Api
          Properties:
            RestApiId: !Ref ApiGateway
            Path: /portfolios/{portfolioId}
            Method: get
            Auth:
              Authorizer: CognitoAuthorizerV2
        UpdatePortfolio:
          Type: Api
          Properties:
            RestApiId: !Ref ApiGateway
            Path: /portfolios/{portfolioId}
            Method: put
            Auth:
              Authorizer: CognitoAuthorizerV2
        DeletePortfolio:
          Type: Api
          Properties:
            RestApiId: !Ref ApiGateway
            Path: /portfolios/{portfolioId}
            Method: delete
            Auth:
              Authorizer: CognitoAuthorizerV2
        AddHolding:
          Type: Api
          Properties:
            RestApiId: !Ref ApiGateway
            Path: /portfolios/{portfolioId}/holdings
            Method: post
            Auth:
              Authorizer: CognitoAuthorizerV2
        AddTransaction:
          Type: Api
          Properties:
            RestApiId: !Ref ApiGateway
            Path: /portfolios/{portfolioId}/transactions
            Method: post
            Auth:
              Authorizer: CognitoAuthorizerV2
        # CORS OPTIONS endpoints for portfolios
        PortfoliosOptions:
          Type: Api
          Properties:
            RestApiId: !Ref ApiGateway
            Path: /portfolios
            Method: options
            Auth:
              Authorizer: NONE
        PortfolioByIdOptions:
          Type: Api
          Properties:
            RestApiId: !Ref ApiGateway
            Path: /portfolios/{portfolioId}
            Method: options
            Auth:
              Authorizer: NONE
        PortfolioHoldingsOptions:
          Type: Api
          Properties:
            RestApiId: !Ref ApiGateway
            Path: /portfolios/{portfolioId}/holdings
            Method: options
            Auth:
              Authorizer: NONE
        PortfolioTransactionsOptions:
          Type: Api
          Properties:
            RestApiId: !Ref ApiGateway
            Path: /portfolios/{portfolioId}/transactions
            Method: options
            Auth:
              Authorizer: NONE

  ReportsAPIFunction:
    Type: AWS::Serverless::Function
    Properties:
      CodeUri: src/
      Handler: functions.api.reports.handler
      Runtime: python3.13
      MemorySize: !Ref LambdaMemorySize
      Timeout: !Ref LambdaTimeout
      Environment:
        Variables:
          ENVIRONMENT: !Ref Environment
          REPORT_TABLE: !Ref ReportTable
          USER_TABLE: !Ref UserTable
          USER_POOL_ID:
            !If [CreateNewCognito, !Ref UserPool, !Ref ExistingUserPoolId]
          USER_POOL_CLIENT_ID:
            !If [
              CreateNewCognito,
              !Ref UserPoolClient,
              !Ref ExistingUserPoolClientId,
            ]
          LOG_LEVEL: !Ref LogLevel
          POWERTOOLS_SERVICE_NAME: fundflow-reports-api
          POWERTOOLS_METRICS_NAMESPACE: FundFlow
      Policies:
        - DynamoDBCrudPolicy:
            TableName: !Ref ReportTable
        - DynamoDBReadPolicy:
            TableName: !Ref UserTable
      Events:
        ListReports:
          Type: Api
          Properties:
            RestApiId: !Ref ApiGateway
            Path: /reports
            Method: get
            Auth:
              Authorizer: CognitoAuthorizerV2
        CreateReport:
          Type: Api
          Properties:
            RestApiId: !Ref ApiGateway
            Path: /reports
            Method: post
            Auth:
              Authorizer: CognitoAuthorizerV2
        GetReport:
          Type: Api
          Properties:
            RestApiId: !Ref ApiGateway
            Path: /reports/{reportId}
            Method: get
            Auth:
              Authorizer: CognitoAuthorizerV2
        UpdateReport:
          Type: Api
          Properties:
            RestApiId: !Ref ApiGateway
            Path: /reports/{reportId}
            Method: put
            Auth:
              Authorizer: CognitoAuthorizerV2
        DeleteReport:
          Type: Api
          Properties:
            RestApiId: !Ref ApiGateway
            Path: /reports/{reportId}
            Method: delete
            Auth:
              Authorizer: CognitoAuthorizerV2
        # CORS OPTIONS endpoints for reports
        ReportsOptions:
          Type: Api
          Properties:
            RestApiId: !Ref ApiGateway
            Path: /reports
            Method: options
            Auth:
              Authorizer: NONE
        ReportByIdOptions:
          Type: Api
          Properties:
            RestApiId: !Ref ApiGateway
            Path: /reports/{reportId}
            Method: options
            Auth:
              Authorizer: NONE

  AuthAPIFunction:
    Type: AWS::Serverless::Function
    Properties:
      CodeUri: src/
      Handler: functions.api.auth.handler
      Runtime: python3.13
      MemorySize: !Ref LambdaMemorySize
      Timeout: !Ref LambdaTimeout
      Environment:
        Variables:
          ENVIRONMENT: !Ref Environment
          USER_POOL_ID:
            !If [CreateNewCognito, !Ref UserPool, !Ref ExistingUserPoolId]
          USER_POOL_CLIENT_ID:
            !If [
              CreateNewCognito,
              !Ref UserPoolClient,
              !Ref ExistingUserPoolClientId,
            ]
          LOG_LEVEL: !Ref LogLevel
          POWERTOOLS_SERVICE_NAME: fundflow-auth-api
          POWERTOOLS_METRICS_NAMESPACE: FundFlow
          SESSION_TIMEOUT_SECONDS: "3600" # 1 hour
          REFRESH_THRESHOLD_SECONDS: "900" # 15 minutes
          CSRF_SECRET_KEY: !Sub "${AWS::StackName}-csrf-secret-${Environment}"
      Policies:
        - Statement:
            - Effect: Allow
              Action:
                - cognito-idp:ForgotPassword
                - cognito-idp:ConfirmForgotPassword
                - cognito-idp:ChangePassword
              Resource: !Sub "arn:aws:cognito-idp:${AWS::Region}:${AWS::AccountId}:userpool/${ExistingUserPoolId}"
      Events:
        ForgotPassword:
          Type: Api
          Properties:
            RestApiId: !Ref ApiGateway
            Path: /auth/forgot-password
            Method: post
        ConfirmForgotPassword:
          Type: Api
          Properties:
            RestApiId: !Ref ApiGateway
            Path: /auth/confirm-forgot-password
            Method: post
        ChangePassword:
          Type: Api
          Properties:
            RestApiId: !Ref ApiGateway
            Path: /auth/change-password
            Method: put
            Auth:
              Authorizer: CognitoAuthorizerV2
        Logout:
          Type: Api
          Properties:
            RestApiId: !Ref ApiGateway
            Path: /auth/session
            Method: delete
            Auth:
              Authorizer: CognitoAuthorizerV2
        # CORS OPTIONS endpoints for auth
        AuthChangePasswordOptions:
          Type: Api
          Properties:
            RestApiId: !Ref ApiGateway
            Path: /auth/change-password
            Method: options
            Auth:
              Authorizer: NONE
        AuthSessionOptions:
          Type: Api
          Properties:
            RestApiId: !Ref ApiGateway
            Path: /auth/session
            Method: options
            Auth:
              Authorizer: NONE

  FundExtractorFunction:
    Type: AWS::Serverless::Function
    Properties:
      CodeUri: src/
      Handler: functions.api.fund_extractor.handler
      Runtime: python3.13
      MemorySize: 1024 # Increased memory for AI processing
      Timeout: 300 # 5 minutes for AI API calls
      Environment:
        Variables:
          ENVIRONMENT: !Ref Environment
          FUND_TABLE: !Ref FundTable
          HISTORICAL_PRICES_TABLE: !Ref HistoricalPricesTable
          USER_TABLE: !Ref UserTable
          PDF_JOB_TABLE: !Ref PDFJobTable
          COGNITO_USER_POOL_ID: !Ref ExistingUserPoolId
          COGNITO_APP_CLIENT_ID: !Ref ExistingUserPoolClientId
          LOG_LEVEL: !Ref LogLevel
          POWERTOOLS_SERVICE_NAME: fundflow-pdf-extractor
          POWERTOOLS_METRICS_NAMESPACE: FundFlow
          OPENROUTER_API_KEY: !Ref OpenRouterAPIKey
      Policies:
        - DynamoDBCrudPolicy:
            TableName: !Ref FundTable
        - DynamoDBCrudPolicy:
            TableName: !Ref HistoricalPricesTable
        - DynamoDBReadPolicy:
            TableName: !Ref UserTable
        - DynamoDBCrudPolicy:
            TableName: !Ref PDFJobTable
      Events:
        ExtractPDF:
          Type: Api
          Properties:
            RestApiId: !Ref ApiGateway
            Path: /funds/extract-pdf
            Method: post
            Auth:
              Authorizer: CognitoAuthorizerV2
        # CORS OPTIONS endpoint for PDF extractor
        ExtractPDFOptions:
          Type: Api
          Properties:
            RestApiId: !Ref ApiGateway
            Path: /funds/extract-pdf
            Method: options
            Auth:
              Authorizer: NONE

  JobsFunction:
    Type: AWS::Serverless::Function
    Properties:
      CodeUri: src/
      Handler: functions.api.jobs.handler
      Runtime: python3.13
      MemorySize: !Ref LambdaMemorySize
      Timeout: !Ref LambdaTimeout
      Environment:
        Variables:
          ENVIRONMENT: !Ref Environment
          PDF_JOB_TABLE: !Ref PDFJobTable
          PDF_EXTRACTOR_FUNCTION_NAME: !Ref FundExtractorFunction
          LOG_LEVEL: !Ref LogLevel
          POWERTOOLS_SERVICE_NAME: fundflow-pdf-jobs
          POWERTOOLS_METRICS_NAMESPACE: FundFlow
      Policies:
        - DynamoDBCrudPolicy:
            TableName: !Ref PDFJobTable
        - Statement:
            - Effect: Allow
              Action:
                - lambda:InvokeFunction
              Resource: !GetAtt FundExtractorFunction.Arn
      Events:
        SubmitPDFJob:
          Type: Api
          Properties:
            RestApiId: !Ref ApiGateway
            Path: /pdf-jobs
            Method: post
            Auth:
              Authorizer: CognitoAuthorizerV2
        GetPDFJobStatus:
          Type: Api
          Properties:
            RestApiId: !Ref ApiGateway
            Path: /pdf-jobs/{jobId}
            Method: get
            Auth:
              Authorizer: CognitoAuthorizerV2
        ListPDFJobs:
          Type: Api
          Properties:
            RestApiId: !Ref ApiGateway
            Path: /pdf-jobs
            Method: get
            Auth:
              Authorizer: CognitoAuthorizerV2
        # CORS OPTIONS endpoints for PDF jobs
        PDFJobsOptions:
          Type: Api
          Properties:
            RestApiId: !Ref ApiGateway
            Path: /pdf-jobs
            Method: options
            Auth:
              Authorizer: NONE
        PDFJobByIdOptions:
          Type: Api
          Properties:
            RestApiId: !Ref ApiGateway
            Path: /pdf-jobs/{jobId}
            Method: options
            Auth:
              Authorizer: NONE

  # DynamoDB Tables
  FundTable:
    Type: AWS::DynamoDB::Table
    Properties:
      TableName: !Sub "${AWS::StackName}-funds"
      BillingMode: !Ref DynamoDBBillingMode
      ProvisionedThroughput: !If
        - IsProvisionedBilling
        - ReadCapacityUnits: !Ref DynamoDBReadCapacity
          WriteCapacityUnits: !Ref DynamoDBWriteCapacity
        - !Ref "AWS::NoValue"
      AttributeDefinitions:
        - AttributeName: fund_id
          AttributeType: S
        - AttributeName: created_at
          AttributeType: S
        - AttributeName: fund_type
          AttributeType: S
        - AttributeName: status
          AttributeType: S
      KeySchema:
        - AttributeName: fund_id
          KeyType: HASH
      GlobalSecondaryIndexes:
        - IndexName: fund_type_index
          KeySchema:
            - AttributeName: fund_type
              KeyType: HASH
            - AttributeName: created_at
              KeyType: RANGE
          Projection:
            ProjectionType: ALL
          ProvisionedThroughput: !If
            - IsProvisionedBilling
            - ReadCapacityUnits: !Ref DynamoDBReadCapacity
              WriteCapacityUnits: !Ref DynamoDBWriteCapacity
            - !Ref "AWS::NoValue"
        - IndexName: status_index
          KeySchema:
            - AttributeName: status
              KeyType: HASH
            - AttributeName: created_at
              KeyType: RANGE
          Projection:
            ProjectionType: ALL
          ProvisionedThroughput: !If
            - IsProvisionedBilling
            - ReadCapacityUnits: !Ref DynamoDBReadCapacity
              WriteCapacityUnits: !Ref DynamoDBWriteCapacity
            - !Ref "AWS::NoValue"
      PointInTimeRecoverySpecification:
        PointInTimeRecoveryEnabled: !If [IsProduction, true, false]
      Tags:
        - Key: Environment
          Value: !Ref Environment
        - Key: Application
          Value: FundFlow

  UserTable:
    Type: AWS::DynamoDB::Table
    Properties:
      TableName: !Sub "${AWS::StackName}-users"
      BillingMode: !Ref DynamoDBBillingMode
      ProvisionedThroughput: !If
        - IsProvisionedBilling
        - ReadCapacityUnits: !Ref DynamoDBReadCapacity
          WriteCapacityUnits: !Ref DynamoDBWriteCapacity
        - !Ref "AWS::NoValue"
      AttributeDefinitions:
        - AttributeName: user_id
          AttributeType: S
        - AttributeName: email
          AttributeType: S
        - AttributeName: role
          AttributeType: S
        - AttributeName: created_at
          AttributeType: S
      KeySchema:
        - AttributeName: user_id
          KeyType: HASH
      GlobalSecondaryIndexes:
        - IndexName: email_index
          KeySchema:
            - AttributeName: email
              KeyType: HASH
          Projection:
            ProjectionType: ALL
          ProvisionedThroughput: !If
            - IsProvisionedBilling
            - ReadCapacityUnits: !Ref DynamoDBReadCapacity
              WriteCapacityUnits: !Ref DynamoDBWriteCapacity
            - !Ref "AWS::NoValue"
        - IndexName: role_index
          KeySchema:
            - AttributeName: role
              KeyType: HASH
            - AttributeName: created_at
              KeyType: RANGE
          Projection:
            ProjectionType: ALL
          ProvisionedThroughput: !If
            - IsProvisionedBilling
            - ReadCapacityUnits: !Ref DynamoDBReadCapacity
              WriteCapacityUnits: !Ref DynamoDBWriteCapacity
            - !Ref "AWS::NoValue"
      PointInTimeRecoverySpecification:
        PointInTimeRecoveryEnabled: !If [IsProduction, true, false]
      Tags:
        - Key: Environment
          Value: !Ref Environment
        - Key: Application
          Value: FundFlow

  PortfolioTable:
    Type: AWS::DynamoDB::Table
    Properties:
      TableName: !Sub "${AWS::StackName}-portfolios"
      BillingMode: !Ref DynamoDBBillingMode
      ProvisionedThroughput: !If
        - IsProvisionedBilling
        - ReadCapacityUnits: !Ref DynamoDBReadCapacity
          WriteCapacityUnits: !Ref DynamoDBWriteCapacity
        - !Ref "AWS::NoValue"
      AttributeDefinitions:
        - AttributeName: portfolio_id
          AttributeType: S
        - AttributeName: user_id
          AttributeType: S
        - AttributeName: created_at
          AttributeType: S
      KeySchema:
        - AttributeName: portfolio_id
          KeyType: HASH
      GlobalSecondaryIndexes:
        - IndexName: UserPortfoliosIndex
          KeySchema:
            - AttributeName: user_id
              KeyType: HASH
            - AttributeName: created_at
              KeyType: RANGE
          Projection:
            ProjectionType: ALL
          ProvisionedThroughput: !If
            - IsProvisionedBilling
            - ReadCapacityUnits: !Ref DynamoDBReadCapacity
              WriteCapacityUnits: !Ref DynamoDBWriteCapacity
            - !Ref "AWS::NoValue"
      PointInTimeRecoverySpecification:
        PointInTimeRecoveryEnabled: true
      DeletionProtectionEnabled: !If [IsProduction, true, false]
      Tags:
        - Key: Name
          Value: !Sub "${AWS::StackName}-portfolios"
        - Key: Environment
          Value: !Ref Environment
        - Key: Application
          Value: FundFlow

  ReportTable:
    Type: AWS::DynamoDB::Table
    Properties:
      TableName: !Sub "${AWS::StackName}-reports"
      BillingMode: !Ref DynamoDBBillingMode
      ProvisionedThroughput: !If
        - IsProvisionedBilling
        - ReadCapacityUnits: !Ref DynamoDBReadCapacity
          WriteCapacityUnits: !Ref DynamoDBWriteCapacity
        - !Ref "AWS::NoValue"
      AttributeDefinitions:
        - AttributeName: report_id
          AttributeType: S
        - AttributeName: user_id
          AttributeType: S
        - AttributeName: created_at
          AttributeType: S
        - AttributeName: report_type
          AttributeType: S
      KeySchema:
        - AttributeName: report_id
          KeyType: HASH
      GlobalSecondaryIndexes:
        - IndexName: user_reports_index
          KeySchema:
            - AttributeName: user_id
              KeyType: HASH
            - AttributeName: created_at
              KeyType: RANGE
          Projection:
            ProjectionType: ALL
          ProvisionedThroughput: !If
            - IsProvisionedBilling
            - ReadCapacityUnits: !Ref DynamoDBReadCapacity
              WriteCapacityUnits: !Ref DynamoDBWriteCapacity
            - !Ref "AWS::NoValue"
        - IndexName: report_type_index
          KeySchema:
            - AttributeName: report_type
              KeyType: HASH
            - AttributeName: created_at
              KeyType: RANGE
          Projection:
            ProjectionType: ALL
          ProvisionedThroughput: !If
            - IsProvisionedBilling
            - ReadCapacityUnits: !Ref DynamoDBReadCapacity
              WriteCapacityUnits: !Ref DynamoDBWriteCapacity
            - !Ref "AWS::NoValue"
      PointInTimeRecoverySpecification:
        PointInTimeRecoveryEnabled: !If [IsProduction, true, false]
      Tags:
        - Key: Environment
          Value: !Ref Environment
        - Key: Application
          Value: FundFlow

  PDFJobTable:
    Type: AWS::DynamoDB::Table
    Properties:
      TableName: !Sub "${AWS::StackName}-pdf-jobs"
      BillingMode: !Ref DynamoDBBillingMode
      ProvisionedThroughput: !If
        - IsProvisionedBilling
        - ReadCapacityUnits: !Ref DynamoDBReadCapacity
          WriteCapacityUnits: !Ref DynamoDBWriteCapacity
        - !Ref "AWS::NoValue"
      AttributeDefinitions:
        - AttributeName: jobId
          AttributeType: S
        - AttributeName: userId
          AttributeType: S
        - AttributeName: createdAt
          AttributeType: S
      KeySchema:
        - AttributeName: jobId
          KeyType: HASH
      GlobalSecondaryIndexes:
        - IndexName: UserIdIndex
          KeySchema:
            - AttributeName: userId
              KeyType: HASH
            - AttributeName: createdAt
              KeyType: RANGE
          Projection:
            ProjectionType: ALL
          ProvisionedThroughput: !If
            - IsProvisionedBilling
            - ReadCapacityUnits: !Ref DynamoDBReadCapacity
              WriteCapacityUnits: !Ref DynamoDBWriteCapacity
            - !Ref "AWS::NoValue"
      TimeToLiveSpecification:
        AttributeName: ttl
        Enabled: true
      PointInTimeRecoverySpecification:
        PointInTimeRecoveryEnabled: !If [IsProduction, true, false]
      Tags:
        - Key: Environment
          Value: !Ref Environment
        - Key: Application
          Value: FundFlow

  HistoricalPricesTable:
    Type: AWS::DynamoDB::Table
    Properties:
      TableName: !Sub "${AWS::StackName}-historical-prices"
      BillingMode: !Ref DynamoDBBillingMode
      ProvisionedThroughput: !If
        - IsProvisionedBilling
        - ReadCapacityUnits: !Ref DynamoDBReadCapacity
          WriteCapacityUnits: !Ref DynamoDBWriteCapacity
        - !Ref "AWS::NoValue"
      AttributeDefinitions:
        - AttributeName: fund_id
          AttributeType: S
        - AttributeName: date
          AttributeType: S
      KeySchema:
        - AttributeName: fund_id
          KeyType: HASH
        - AttributeName: date
          KeyType: RANGE
      GlobalSecondaryIndexes:
        - IndexName: date_index
          KeySchema:
            - AttributeName: date
              KeyType: HASH
            - AttributeName: fund_id
              KeyType: RANGE
          Projection:
            ProjectionType: ALL
          ProvisionedThroughput: !If
            - IsProvisionedBilling
            - ReadCapacityUnits: !Ref DynamoDBReadCapacity
              WriteCapacityUnits: !Ref DynamoDBWriteCapacity
            - !Ref "AWS::NoValue"
      PointInTimeRecoverySpecification:
        PointInTimeRecoveryEnabled: !If [IsProduction, true, false]
      Tags:
        - Key: Environment
          Value: !Ref Environment
        - Key: Application
          Value: FundFlow

  FundSnapshotTable:
    Type: AWS::DynamoDB::Table
    Properties:
      TableName: !Sub "${AWS::StackName}-fund-snapshots"
      BillingMode: !Ref DynamoDBBillingMode
      ProvisionedThroughput: !If
        - IsProvisionedBilling
        - ReadCapacityUnits: !Ref DynamoDBReadCapacity
          WriteCapacityUnits: !Ref DynamoDBWriteCapacity
        - !Ref "AWS::NoValue"
      AttributeDefinitions:
        - AttributeName: fund_id
          AttributeType: S
        - AttributeName: snapshot_month
          AttributeType: S
        - AttributeName: created_at
          AttributeType: S
      KeySchema:
        - AttributeName: fund_id
          KeyType: HASH
        - AttributeName: snapshot_month
          KeyType: RANGE
      LocalSecondaryIndexes:
        - IndexName: fund_created_at_index
          KeySchema:
            - AttributeName: fund_id
              KeyType: HASH
            - AttributeName: created_at
              KeyType: RANGE
          Projection:
            ProjectionType: ALL
      PointInTimeRecoverySpecification:
        PointInTimeRecoveryEnabled: !If [IsProduction, true, false]
      Tags:
        - Key: Environment
          Value: !Ref Environment
        - Key: Application
          Value: FundFlow

  #  Buckets
  StaticAssetsBucket:
    Type: AWS::S3::Bucket
    Properties:
      BucketName: !Sub "${AWS::StackName}-static-assets-${AWS::AccountId}"
      PublicAccessBlockConfiguration:
        BlockPublicAcls: false
        BlockPublicPolicy: false
        IgnorePublicAcls: false
        RestrictPublicBuckets: false
      BucketEncryption:
        ServerSideEncryptionConfiguration:
          - ServerSideEncryptionByDefault:
              SSEAlgorithm: AES256
      VersioningConfiguration:
        Status: !If [IsProduction, Enabled, Suspended]
      Tags:
        - Key: Environment
          Value: !Ref Environment
        - Key: Application
          Value: FundFlow

  ReportsBucket:
    Type: AWS::S3::Bucket
    Properties:
      BucketName: !Sub "${AWS::StackName}-reports-${AWS::AccountId}"
      PublicAccessBlockConfiguration:
        BlockPublicAcls: true
        BlockPublicPolicy: true
        IgnorePublicAcls: true
        RestrictPublicBuckets: true
      BucketEncryption:
        ServerSideEncryptionConfiguration:
          - ServerSideEncryptionByDefault:
              SSEAlgorithm: AES256
      VersioningConfiguration:
        Status: !If [IsProduction, Enabled, Suspended]
      LifecycleConfiguration:
        Rules:
          - Id: DeleteOldReports
            Status: Enabled
            ExpirationInDays: !Ref BackupRetentionDays
      Tags:
        - Key: Environment
          Value: !Ref Environment
        - Key: Application
          Value: FundFlow

  # CloudFront Distribution
  CloudFrontDistribution:
    Type: AWS::CloudFront::Distribution
    Properties:
      DistributionConfig:
        Comment: !Sub "FundFlow ${Environment} CloudFront Distribution"
        DefaultCacheBehavior:
          TargetOriginId: S3Origin
          ViewerProtocolPolicy: redirect-to-https
          CachePolicyId: 658327ea-f89d-4fab-a63d-7e88639e58f6 # Managed-CachingOptimized
          OriginRequestPolicyId: 88a5eaf4-2fd4-4709-b370-b4c650ea3fcf # Managed-CORS-S3Origin
          DefaultTTL: !Ref CloudFrontDefaultTTL
          MaxTTL: !Ref CloudFrontMaxTTL
          Compress: !Ref CloudFrontCompressionEnabled
        Origins:
          - Id: S3Origin
            DomainName: !GetAtt StaticAssetsBucket.RegionalDomainName
            S3OriginConfig:
              OriginAccessIdentity: !Sub "origin-access-identity/cloudfront/${CloudFrontOriginAccessIdentity}"
        Aliases: !If
          - HasCustomDomain
          - [!Ref DomainName]
          - !Ref "AWS::NoValue"
        ViewerCertificate: !If
          - HasCustomDomain
          - AcmCertificateArn: !Ref CertificateArn
            SslSupportMethod: sni-only
            MinimumProtocolVersion: TLSv1.2_2021
          - CloudFrontDefaultCertificate: true
        Enabled: true
        DefaultRootObject: index.html
        CustomErrorResponses:
          - ErrorCode: 404
            ResponseCode: 200
            ResponsePagePath: /index.html
        PriceClass: !Ref CloudFrontPriceClass
      Tags:
        - Key: Environment
          Value: !Ref Environment
        - Key: Application
          Value: FundFlow

  CloudFrontOriginAccessIdentity:
    Type: AWS::CloudFront::CloudFrontOriginAccessIdentity
    Properties:
      CloudFrontOriginAccessIdentityConfig:
        Comment: !Sub "FundFlow ${Environment} OAI"

  # S3 Bucket Policy for CloudFront
  StaticAssetsBucketPolicy:
    Type: AWS::S3::BucketPolicy
    Properties:
      Bucket: !Ref StaticAssetsBucket
      PolicyDocument:
        Statement:
          - Effect: Allow
            Principal:
              CanonicalUser: !GetAtt CloudFrontOriginAccessIdentity.S3CanonicalUserId
            Action: s3:GetObject
            Resource: !Sub "arn:aws:s3:::${StaticAssetsBucket}/*"
          - Effect: Allow
            Principal: "*"
            Action: s3:GetObject
            Resource: !Sub "arn:aws:s3:::${StaticAssetsBucket}/fund-manager-photos/*"

  # API Gateway
  ApiGateway:
    Type: AWS::Serverless::Api
    Properties:
      Name: !Sub "${AWS::StackName}-api"
      StageName: !Ref Environment
      BinaryMediaTypes:
        - "multipart/form-data"
        - "application/pdf"
        - "application/octet-stream"
      Cors:
        AllowMethods: "'GET,POST,PUT,DELETE,OPTIONS'"
        AllowHeaders: "'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token,Accept,Accept-Encoding,Accept-Language,Cache-Control,Connection,Host,Origin,Pragma,Referer,User-Agent'"
        AllowOrigin: "'*'"
        MaxAge: "'600'"
      MethodSettings:
        - ResourcePath: "/*"
          HttpMethod: "*"
          ThrottlingBurstLimit: !Ref ApiThrottleBurstLimit
          ThrottlingRateLimit: !Ref ApiThrottleRateLimit
          LoggingLevel: !If [IsProduction, INFO, ERROR]
          DataTraceEnabled: !If [IsProduction, false, true]
          MetricsEnabled: !Ref EnableDetailedMonitoring
          CachingEnabled: !Ref ApiCachingEnabled
          CacheTtlInSeconds: !Ref ApiCacheTTL
      Auth:
        DefaultAuthorizer: CognitoAuthorizerV2
        Authorizers:
          CognitoAuthorizerV2:
            UserPoolArn: !If
              - CreateNewCognito
              - !GetAtt UserPool.Arn
              - !Sub "arn:aws:cognito-idp:${AWS::Region}:${AWS::AccountId}:userpool/${ExistingUserPoolId}"
            # Remove AuthorizationScopes to allow any valid token from the user pool
            # This allows both ID tokens and access tokens to work
            # Custom authorization logic can be handled in the Lambda functions if needed
      GatewayResponses:
        DEFAULT_4XX:
          ResponseTemplates:
            "application/json": '{"message":$context.error.messageString}'
          ResponseParameters:
            Headers:
              Access-Control-Allow-Origin: "'*'"
              Access-Control-Allow-Headers: "'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token,Accept,Accept-Encoding,Accept-Language,Cache-Control,Connection,Host,Origin,Pragma,Referer,User-Agent'"
        DEFAULT_5XX:
          ResponseTemplates:
            "application/json": '{"message":$context.error.messageString}'
          ResponseParameters:
            Headers:
              Access-Control-Allow-Origin: "'*'"
              Access-Control-Allow-Headers: "'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token,Accept,Accept-Encoding,Accept-Language,Cache-Control,Connection,Host,Origin,Pragma,Referer,User-Agent'"
    Tags:
      Environment: !Ref Environment
      Application: FundFlow

  # Note: Using existing Cognito User Pool and Client
  # UserPool and UserPoolClient resources are commented out
  # to use existing ones via parameters

  # CloudWatch Log Groups for Lambda Functions
  HealthCheckLogGroup:
    Type: AWS::Logs::LogGroup
    Properties:
      LogGroupName: !Sub "/aws/lambda/${AWS::StackName}-HealthCheckFunction"
      RetentionInDays: !Ref LogRetentionDays

  FundsAPILogGroup:
    Type: AWS::Logs::LogGroup
    Properties:
      LogGroupName: !Sub "/aws/lambda/${AWS::StackName}-FundsAPIFunction"
      RetentionInDays: !Ref LogRetentionDays

  UsersAPILogGroup:
    Type: AWS::Logs::LogGroup
    Properties:
      LogGroupName: !Sub "/aws/lambda/${AWS::StackName}-UsersAPIFunction"
      RetentionInDays: !Ref LogRetentionDays

  ReportsAPILogGroup:
    Type: AWS::Logs::LogGroup
    Properties:
      LogGroupName: !Sub "/aws/lambda/${AWS::StackName}-ReportsAPIFunction"
      RetentionInDays: !Ref LogRetentionDays

  AuthAPILogGroup:
    Type: AWS::Logs::LogGroup
    Properties:
      LogGroupName: !Sub "/aws/lambda/${AWS::StackName}-AuthAPIFunction"
      RetentionInDays: !Ref LogRetentionDays

  # CloudWatch Alarms (conditionally created if email is provided)
  HighErrorRateAlarm:
    Type: AWS::CloudWatch::Alarm
    Condition: HasAlarmEmail
    Properties:
      AlarmName: !Sub "${AWS::StackName}-HighErrorRate"
      AlarmDescription: "High error rate detected in API Gateway"
      MetricName: 4XXError
      Namespace: AWS/ApiGateway
      Statistic: Sum
      Period: 300
      EvaluationPeriods: 2
      Threshold: 10
      ComparisonOperator: GreaterThanThreshold
      AlarmActions:
        - !Ref AlarmTopic
      Dimensions:
        - Name: ApiName
          Value: !Sub "${AWS::StackName}-api"

  AlarmTopic:
    Type: AWS::SNS::Topic
    Condition: HasAlarmEmail
    Properties:
      TopicName: !Sub "${AWS::StackName}-alarms"
      Subscription:
        - Protocol: email
          Endpoint: !Ref AlarmNotificationEmail

Outputs:
  ApiGatewayUrl:
    Description: "API Gateway endpoint URL"
    Value: !Sub "https://${ApiGateway}.execute-api.${AWS::Region}.amazonaws.com/${Environment}"
    Export:
      Name: !Sub "${AWS::StackName}-ApiUrl"

  CloudFrontUrl:
    Description: "CloudFront distribution URL"
    Value: !Sub "https://${CloudFrontDistribution.DomainName}"
    Export:
      Name: !Sub "${AWS::StackName}-CloudFrontUrl"

  FundTableName:
    Description: "DynamoDB Fund table name"
    Value: !Ref FundTable
    Export:
      Name: !Sub "${AWS::StackName}-FundTable"

  UserTableName:
    Description: "DynamoDB User table name"
    Value: !Ref UserTable
    Export:
      Name: !Sub "${AWS::StackName}-UserTable"

  PortfolioTableName:
    Description: "DynamoDB Portfolio table name"
    Value: !Ref PortfolioTable
    Export:
      Name: !Sub "${AWS::StackName}-PortfolioTable"

  ReportTableName:
    Description: "DynamoDB Report table name"
    Value: !Ref ReportTable
    Export:
      Name: !Sub "${AWS::StackName}-ReportTable"

  PDFJobTableName:
    Description: "DynamoDB PDF Job table name"
    Value: !Ref PDFJobTable
    Export:
      Name: !Sub "${AWS::StackName}-PDFJobTable"

  StaticAssetsBucketName:
    Description: "S3 bucket for static assets"
    Value: !Ref StaticAssetsBucket
    Export:
      Name: !Sub "${AWS::StackName}-StaticAssetsBucket"

  ReportsBucketName:
    Description: "S3 bucket for reports"
    Value: !Ref ReportsBucket
    Export:
      Name: !Sub "${AWS::StackName}-ReportsBucket"

  UserPoolId:
    Description: "Cognito User Pool ID"
    Value: !If [CreateNewCognito, !Ref UserPool, !Ref ExistingUserPoolId]
    Export:
      Name: !Sub "${AWS::StackName}-UserPoolId"

  UserPoolClientId:
    Description: "Cognito User Pool Client ID"
    Value:
      !If [CreateNewCognito, !Ref UserPoolClient, !Ref ExistingUserPoolClientId]
    Export:
      Name: !Sub "${AWS::StackName}-UserPoolClientId"

  UserPoolResourceServerIdentifier:
    Condition: CreateNewCognito
    Description: "Cognito Resource Server Identifier"
    Value: !Ref CognitoResourceServerName
    Export:
      Name: !Sub "${AWS::StackName}-ResourceServerIdentifier"
